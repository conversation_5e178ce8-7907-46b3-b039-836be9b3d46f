"""
属性筛选智能体 (Attribute Filtering Agent)
与PostgreSQL关系型数据库交互，实现Text2SQL功能
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from pydantic import BaseModel
from src.agents.base_agent import BaseAgent
from src.agents.data_models import QueryResult, UnifiedStructuredDataTask, SearchResultItem
from src.models.LLM import MultiProviderLLM
from src.config import Config

logger = logging.getLogger(__name__)


class StructuredQueryResult(BaseModel):
    """结构化查询结果模型"""
    success: bool
    sql_query: Optional[str] = None
    results: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class AttributeFilteringAgent(BaseAgent):
    """
    属性筛选智能体
    
    该智能体专门处理基于物理属性、分类标签及其他元数据的精确查询任务。
    它将用户的自然语言查询转换为SQL语句，与PostgreSQL数据库交互。
    """
    
    def __init__(self, agent_name: str = "AttributeFilteringAgent"):
        super().__init__(agent_name)
        self.conn = None
        self.cursor = None
        self.llm = None
        self.schema_info = None
        self._connected = False
        
    async def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            # 建立数据库连接
            db_config = Config.POSTGRES_CONFIG
            self.conn = psycopg2.connect(**db_config)
            # 默认不自动提交，手动管理事务
            self.conn.autocommit = False
            self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            
            # 初始化LLM
            self.llm = MultiProviderLLM()
            
            # 获取数据库模式信息
            await self._load_schema_info()
            
            logger.info(f"{self.agent_name} 成功连接到PostgreSQL数据库")
            self._connected = True
            
        except Exception as e:
            logger.error(f"{self.agent_name} 连接PostgreSQL失败: {e}")
            raise
    
    async def disconnect(self):
        """断开数据库连接"""
        try:
            # 先回滚任何未完成的事务
            if self.conn:
                try:
                    self.conn.rollback()
                except Exception:
                    pass
                
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
                
            self._connected = False
            logger.info(f"{self.agent_name} 已断开PostgreSQL连接")
        except Exception as e:
            logger.error(f"断开连接时出错: {e}")
    
    async def _load_schema_info(self):
        """加载数据库模式信息"""
        try:
            # 直接使用默认模式信息，不从数据库获取
            self.schema_info = self._get_default_schema()
            logger.info("数据库默认模式信息加载完成")
            
        except Exception as e:
            logger.error(f"加载数据库模式信息失败: {e}")
            # 使用默认模式信息
            self.schema_info = self._get_default_schema()
    
    def _get_table_description(self, table_name: str) -> str:
        """获取表的描述信息"""
        descriptions = {
            "parts": "CAD零件表，存储零件的几何属性、材料属性、制造特征等信息",
            "assemblies": "CAD装配体表，存储装配体的几何属性、行业分类、零件数量等信息"
        }
        return descriptions.get(table_name, "未知表")
    
    def _get_column_description(self, column_name: str) -> str:
        """获取列的描述信息"""
        descriptions = {
            "uuid": "唯一标识符",
            "top_level_assembly_id": "顶层装配体ID",
            "name": "名称",
            "material": "材料类型",
            "description": "描述信息",
            "length": "长度（厘米）",
            "width": "宽度（厘米）",
            "height": "高度（厘米）",
            "area": "表面积（平方厘米）",
            "volume": "体积（立方厘米）",
            "density": "密度（千克/立方厘米）",
            "mass": "质量（千克）",
            "hole_count": "孔的数量",
            "hole_diameter_mean": "孔的平均直径（厘米）",
            "hole_diameter_std": "孔直径的标准差",
            "hole_depth_mean": "孔的平均深度（厘米）",
            "hole_depth_std": "孔深度的标准差",
            "part_count": "包含的零件数量",
            "industry": "所属行业",
            "category": "分类"
        }
        return descriptions.get(column_name, "")
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认的数据库模式信息"""
        return {
            "tables": {
                "parts": {
                    "description": "CAD零件表，存储零件的几何属性、材料属性、制造特征等信息",
                    "columns": [
                        {"name": "uuid", "type": "text", "description": "唯一标识符"},
                        {"name": "top_level_assembly_id", "type": "text", "description": "顶层装配体ID"},
                        {"name": "name", "type": "text", "description": "零件名称"},
                        {"name": "material", "type": "text", "description": "材料类型"},
                        {"name": "description", "type": "text", "description": "描述信息"},
                        {"name": "length", "type": "float", "description": "长度（厘米）"},
                        {"name": "width", "type": "float", "description": "宽度（厘米）"},
                        {"name": "height", "type": "float", "description": "高度（厘米）"},
                        {"name": "area", "type": "float", "description": "表面积（平方厘米）"},
                        {"name": "volume", "type": "float", "description": "体积（立方厘米）"},
                        {"name": "density", "type": "float", "description": "密度（千克/立方厘米）"},
                        {"name": "mass", "type": "float", "description": "质量（千克）"},
                        {"name": "hole_count", "type": "integer", "description": "孔的数量"},
                        {"name": "hole_diameter_mean", "type": "float", "description": "孔的平均直径（厘米）"},
                        {"name": "hole_diameter_std", "type": "float", "description": "孔直径的标准差"},
                        {"name": "hole_depth_mean", "type": "float", "description": "孔的平均深度（厘米）"},
                        {"name": "hole_depth_std", "type": "float", "description": "孔深度的标准差"}
                    ]
                },
                "assemblies": {
                    "description": "CAD装配体表，存储装配体的几何属性、行业分类、零件数量等信息",
                    "columns": [
                        {"name": "uuid", "type": "text", "description": "唯一标识符"},
                        {"name": "name", "type": "text", "description": "装配体名称"},
                        {"name": "length", "type": "float", "description": "长度（厘米）"},
                        {"name": "width", "type": "float", "description": "宽度（厘米）"},
                        {"name": "height", "type": "float", "description": "高度（厘米）"},
                        {"name": "area", "type": "float", "description": "表面积（平方厘米）"},
                        {"name": "volume", "type": "float", "description": "体积（立方厘米）"},
                        {"name": "density", "type": "float", "description": "密度（千克/立方厘米）"},
                        {"name": "mass", "type": "float", "description": "质量（千克）"},
                        {"name": "part_count", "type": "integer", "description": "包含的零件数量"},
                        {"name": "industry", "type": "text", "description": "所属行业"},
                        {"name": "category", "type": "text", "description": "分类"},
                        {"name": "description", "type": "text", "description": "描述信息"}
                    ]
                }
            },
            "description": "CAD零件和装配体数据库，包含物理属性信息"
        }
    
    def _build_system_prompt(self) -> str:
        """构建Text2SQL的系统提示词"""
        schema_str = self._format_schema_for_prompt()
        
        return f"""You are a professional SQL query generator. Your task is to convert user's natural language questions into accurate PostgreSQL SQL query statements.

Database Schema Information:
{schema_str}

Important Rules:
1. Return only SQL query statements, do not include any explanations or preamble
2. Query statements must conform to PostgreSQL syntax
3. Use correct table and column names, table names are 'parts' or 'assemblies'
4. For fuzzy queries, use ILIKE or SIMILAR TO for fuzzy matching
5. Do not limit the number of returned results, do not add LIMIT clause
6. Only query uuid and description fields, do not use SELECT *, do not return other fields

Common Query Examples:
- Parts queries:
  - Find parts with mass in a certain range: SELECT uuid, description FROM parts WHERE mass BETWEEN 10 AND 100;
  - Find parts of specific material: SELECT uuid, description FROM parts WHERE material ILIKE '%steel%';
  - Find parts with holes: SELECT uuid, description FROM parts WHERE hole_count > 0;

- Assembly queries:
  - Find assemblies of specific category: SELECT uuid, description FROM assemblies WHERE category ILIKE '%engine%';

- Union queries:
  - Find parts and assemblies with mass greater than 100: SELECT uuid, description FROM parts WHERE mass > 100 UNION ALL SELECT uuid, description FROM assemblies WHERE mass > 100;
  """
    
    def _format_schema_for_prompt(self) -> str:
        """格式化数据库模式信息用于提示词"""
        if not self.schema_info:
            return "数据库模式信息未加载"
        
        schema_parts = []
        for table_name, table_info in self.schema_info["tables"].items():
            schema_parts.append(f"\n表名: {table_name}")
            schema_parts.append(f"描述: {table_info['description']}")
            schema_parts.append("列信息:")
            
            for col in table_info["columns"]:
                col_desc = f"  - {col['name']} ({col['type']})"
                if col.get('description'):
                    col_desc += f": {col['description']}"
                schema_parts.append(col_desc)
        
        return "\n".join(schema_parts)
    
    async def execute_task(self, task: UnifiedStructuredDataTask) -> QueryResult:
        """
        执行结构化数据查询任务

        Args:
            task: UnifiedStructuredDataTask实例，包含自然语言查询文本和可选的ID列表

        Returns:
            QueryResult: 包含查询结果的查询结果对象
        """
        import time
        start_time = time.time()

        try:
            # 确保 LLM 已初始化（但不需要数据库连接，因为查询时会创建独立连接）
            if self.llm is None:
                self.llm = MultiProviderLLM()
            
            if self.schema_info is None:
                await self._load_schema_info()
            
            # 将自然语言转换为SQL查询，如果有ID列表则包含在提示中
            sql_query = await self.text_to_sql(task.query_text, task.id_list)

            # 执行SQL查询（使用独立连接，支持并发）
            raw_results = await self._execute_sql_query(sql_query)            # 转换为标准化的SearchResultItem格式
            search_results = []
            for i, result in enumerate(raw_results):
                search_results.append(SearchResultItem(
                    rank=i + 1,
                    uuid=result.get('uuid', str(i)),
                    name=result.get('name', f'Part_{i}'),
                    description=result.get('description', ''),
                    search_type='structured',
                    metadata=result
                ))

            execution_time = time.time() - start_time

            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=search_results,
                execution_time=execution_time,
                total_results=len(search_results)
            )

        except Exception as e:
            logger.error(f"{self.agent_name} 执行任务失败: {e}")
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[],
                execution_time=execution_time,
                total_results=0
            )



    async def _execute_sql_query(self, sql_query: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询（使用独立连接支持并发）

        Args:
            sql_query: SQL查询语句

        Returns:
            查询结果列表
        """
        import asyncio

        def _sync_execute_sql(sql_query: str) -> List[Dict[str, Any]]:
            """同步执行SQL查询的内部函数"""
            # 为每次查询创建独立的数据库连接，避免并发冲突
            conn = None
            cursor = None

            try:
                # 创建新的数据库连接
                db_config = Config.POSTGRES_CONFIG
                conn = psycopg2.connect(**db_config)
                conn.autocommit = True  # 自动提交，避免事务阻塞
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                # 执行查询
                cursor.execute(sql_query)
                rows = cursor.fetchall()

                # 转换为字典列表
                results = [dict(row) for row in rows]

                logger.info(f"SQL查询成功，返回{len(results)}条记录")
                return results

            except Exception as e:
                logger.error(f"SQL查询执行失败: {e}")
                raise

            finally:
                # 确保连接被正确关闭
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()

        # 在线程池中执行同步的SQL查询，实现真正的并行
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _sync_execute_sql, sql_query)
    
    async def text_to_sql(self, query_text: str, id_list: Optional[List[str]] = None) -> str:
        """
        将自然语言查询转换为SQL语句

        Args:
            query_text: 自然语言查询
            id_list: 可选的ID列表，用于缩小查询范围

        Returns:
            生成的SQL查询语句
        """
        try:
            if self.llm is None:
                await self.connect()

            system_prompt = self._build_system_prompt()

            # 构建完整的提示词
            user_prompt = f"用户查询: {query_text}"

            # 如果提供了ID列表，添加约束信息但使用占位符
            if id_list:
                user_prompt += "\n\n额外约束: 只查询特定uuid的记录"
                user_prompt += "\n请在WHERE子句中添加: uuid IN (__ID_LIST_PLACEHOLDER__)"

            full_prompt = f"{system_prompt}\n\n{user_prompt}\n\nSQL查询语句:"

            # 在线程池中调用LLM生成SQL，实现真正的并行
            import asyncio
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self.llm.chat,
                [{"role": "user", "content": full_prompt}]
            )

            # 清理响应，提取SQL语句
            sql_query = self._clean_sql_response(response)

            # 如果有ID列表，替换占位符
            if id_list:
                id_list_str = "', '".join(id_list)
                sql_query = sql_query.replace("__ID_LIST_PLACEHOLDER__", f"'{id_list_str}'")

            logger.info(f"Text2SQL转换完成: {query_text} -> {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Text2SQL转换失败: {e}")
            raise
    
    def _clean_sql_response(self, response: str) -> str:
        """
        清理LLM响应，提取纯净的SQL语句
        
        Args:
            response: LLM的原始响应
            
        Returns:
            清理后的SQL语句
        """
        # 移除markdown代码块标记
        response = response.strip()
        if response.startswith("```sql"):
            response = response[6:]
        elif response.startswith("```"):
            response = response[3:]
        
        if response.endswith("```"):
            response = response[:-3]
        
        # 移除多余的空行和注释
        lines = response.split('\n')
        clean_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('--'):
                clean_lines.append(line)
        
        sql_query = ' '.join(clean_lines)
        
        # 确保以分号结尾
        if not sql_query.endswith(';'):
            sql_query += ';'
        
        return sql_query
    
    async def get_model_paths_for_uuids(self, uuids: List[str]) -> Dict[str, str]:
        """
        根据UUID列表获取对应的模型路径
        
        路径规则:
        1. 如果uuid在assemblies表中存在，路径为 fusion360_assembly/{uuid}/assembly.glb
        2. 否则，通过uuid查询parts表，获取top_level_assembly_id，
           路径为 fusion360_assembly/{top_level_assembly_id}/{uuid}.glb
        
        Args:
            uuids: UUID列表
            
        Returns:
            Dict[str, str]: UUID到模型路径的映射字典
        """
        if not uuids:
            return {}
            
        # 确保数据库连接存在
        if not self.conn or not self.cursor:
            await self.connect()
        
        result_paths = {}
        
        try:
            # 查询assemblies表中存在的UUID
            placeholders = ','.join(['%s'] * len(uuids))
            assembly_query = f"""
                SELECT uuid FROM assemblies
                WHERE uuid IN ({placeholders})
            """
            
            self.cursor.execute(assembly_query, uuids)
            assembly_uuids = {row['uuid'] for row in self.cursor.fetchall()}
            
            # 查询parts表中的UUID和对应的top_level_assembly_id
            parts_query = f"""
                SELECT uuid, top_level_assembly_id FROM parts
                WHERE uuid IN ({placeholders})
            """
            
            self.cursor.execute(parts_query, uuids)
            parts_data = {row['uuid']: row['top_level_assembly_id'] for row in self.cursor.fetchall()}
            
            # 提交事务
            self.conn.commit()
            
            # 为每个UUID生成对应的模型路径
            for uuid in uuids:
                if uuid in assembly_uuids:
                    # 装配体路径
                    result_paths[uuid] = f"assembly/{uuid}/assembly.glb"
                elif uuid in parts_data:
                    # 零件路径
                    top_level_id = parts_data[uuid]
                    result_paths[uuid] = f"assembly/{top_level_id}/{uuid}.glb"
                else:
                    # 未找到对应记录，使用默认路径格式
                    result_paths[uuid] = f"unknown/{uuid}.glb"
                    logger.warning(f"UUID {uuid} 在数据库中未找到对应记录")
            
            return result_paths
            
        except Exception as e:
            # 回滚事务
            if self.conn:
                self.conn.rollback()
            logger.error(f"获取模型路径时出错: {e}")
            # 返回默认路径
            return {uuid: f"unknown/{uuid}.glb" for uuid in uuids}
    

